const mongoose = require('mongoose');

const ProgressSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
    },
    weight: {
        type: Number,
        required: [true, 'Please add current weight'],
        min: [20, 'Weight must be at least 20 kg'],
        max: [300, 'Weight cannot exceed 300 kg']
    },
    bodyFat: {
        type: Number,
        min: [0, 'Body fat percentage cannot be negative'],
        max: [50, 'Body fat percentage cannot exceed 50%']
    },
    notes: {
        type: String,
        maxlength: [1000, 'Notes cannot be more than 1000 characters']
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = mongoose.model('Progress', ProgressSchema);

