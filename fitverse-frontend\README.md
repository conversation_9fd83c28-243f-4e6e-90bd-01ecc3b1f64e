# FitVerse - Frontend

A modern, responsive fitness tracking web application built with React, Vite, and TailwindCSS.

## 🚀 Features

- **User Authentication**: Secure login and registration with JWT
- **Dashboard**: Overview of fitness stats and recent activity
- **Workout Management**: Create and track workout routines
- **Nutrition Planning**: Manage meal plans with macronutrient tracking
- **Progress Tracking**: Monitor weight and body composition changes
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile
- **Beautiful Animations**: Smooth transitions powered by Framer Motion
- **Real-time Charts**: Visual progress tracking with Recharts

## 🛠️ Tech Stack

- **Frontend Framework**: React 18
- **Build Tool**: Vite
- **Styling**: TailwindCSS
- **Animations**: Framer Motion
- **Charts**: Recharts
- **Icons**: Lucide React
- **HTTP Client**: Axios
- **Routing**: React Router DOM

## 📁 Project Structure

```
src/
├── components/
│   ├── Layout.jsx          # Main layout component
│   ├── Navbar.jsx          # Navigation bar
│   └── ProtectedRoute.jsx  # Route protection
├── context/
│   └── AuthContext.jsx     # Authentication context
├── pages/
│   ├── Dashboard.jsx       # Dashboard page
│   ├── Login.jsx          # Login page
│   ├── Register.jsx       # Registration page
│   ├── Workouts.jsx       # Workouts management
│   ├── Nutrition.jsx      # Nutrition planning
│   └── Progress.jsx       # Progress tracking
├── config/
│   └── api.js             # API configuration
├── App.jsx                # Main app component
├── main.jsx              # Entry point
└── index.css             # Global styles
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Backend API running (see backend repository)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fitverse-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

## 📱 Pages Overview

### Authentication Pages
- **Login**: User authentication with email and password
- **Register**: New user registration with fitness goals

### Protected Pages
- **Dashboard**: Overview with stats cards and recent activity
- **Workouts**: Create and manage workout routines
- **Nutrition**: Plan meals and track macronutrients
- **Progress**: Track weight and body composition changes

## 🎨 Design Features

- **Modern UI**: Clean, professional design with TailwindCSS
- **Responsive**: Mobile-first approach with responsive breakpoints
- **Animations**: Smooth page transitions and micro-interactions
- **Charts**: Interactive charts for progress visualization
- **Icons**: Consistent iconography with Lucide React

## 🔧 Configuration

### API Configuration
The app connects to the backend API. Update the API base URL in `src/config/api.js`:

```javascript
const API_BASE_URL = 'http://localhost:5000/api';
```

### Environment Variables
Create a `.env` file in the root directory:

```env
VITE_API_URL=http://localhost:5000/api
```

## 📊 Features in Detail

### Dashboard
- Welcome message with user's name
- Stats cards showing calories, workouts, weight, and goals
- Quick action cards for workouts and nutrition
- Recent activity feed

### Workouts
- Create new workout routines
- Add multiple exercises with sets, reps, and instructions
- Expandable workout details
- Delete workouts

### Nutrition
- Create nutrition plans with daily targets
- Add meals with specific times and foods
- Macronutrient pie charts
- Calorie tracking

### Progress
- Add progress entries with weight and body fat
- Weight progress line chart
- Progress statistics
- Notes for each entry

## 🎯 Key Features

### Authentication
- JWT token-based authentication
- Automatic token refresh
- Protected routes
- Persistent login state

### Responsive Design
- Mobile-first approach
- Tablet and desktop optimized
- Touch-friendly interactions
- Adaptive layouts

### Animations
- Page transitions with Framer Motion
- Micro-interactions on buttons and cards
- Loading states and spinners
- Smooth accordion animations

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

### Deploy to Vercel
1. Install Vercel CLI: `npm i -g vercel`
2. Run: `vercel`
3. Follow the prompts

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please open an issue in the repository.

## 🔗 Related Projects

- [FitVerse Backend](https://github.com/your-username/fitverse-backend) - Node.js + Express + MongoDB API

