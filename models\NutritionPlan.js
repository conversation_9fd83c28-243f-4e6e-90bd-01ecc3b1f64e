const mongoose = require('mongoose');

const MealSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Please add meal name'],
        trim: true,
        maxlength: [100, 'Meal name cannot be more than 100 characters']
    },
    time: {
        type: String,
        required: [true, 'Please add meal time'],
        trim: true
    },
    foods: [{
        name: {
            type: String,
            required: [true, 'Please add food name']
        },
        quantity: {
            type: String,
            required: [true, 'Please add food quantity']
        },
        calories: {
            type: Number,
            required: [true, 'Please add calories']
        }
    }],
    totalCalories: {
        type: Number,
        required: [true, 'Please add total calories for this meal']
    }
});

const NutritionPlanSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
    },
    calories: {
        type: Number,
        required: [true, 'Please add daily calorie target'],
        min: [500, 'Daily calories must be at least 500'],
        max: [10000, 'Daily calories cannot exceed 10000']
    },
    protein: {
        type: Number,
        required: [true, 'Please add daily protein target (grams)'],
        min: [0, 'Protein cannot be negative'],
        max: [500, 'Protein cannot exceed 500 grams']
    },
    carbs: {
        type: Number,
        required: [true, 'Please add daily carbs target (grams)'],
        min: [0, 'Carbs cannot be negative'],
        max: [1000, 'Carbs cannot exceed 1000 grams']
    },
    fat: {
        type: Number,
        required: [true, 'Please add daily fat target (grams)'],
        min: [0, 'Fat cannot be negative'],
        max: [300, 'Fat cannot exceed 300 grams']
    },
    meals: [MealSchema],
    createdAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = mongoose.model('NutritionPlan', NutritionPlanSchema);

