const NutritionPlan = require('../models/NutritionPlan');

// @desc    Create new nutrition plan
// @route   POST /api/nutrition
// @access  Private
const createNutritionPlan = async (req, res, next) => {
    try {
        // Add user to req.body
        req.body.userId = req.user.id;

        const nutritionPlan = await NutritionPlan.create(req.body);

        res.status(201).json({
            success: true,
            data: nutritionPlan
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Get all nutrition plans for a user
// @route   GET /api/nutrition
// @access  Private
const getNutritionPlans = async (req, res, next) => {
    try {
        const nutritionPlans = await NutritionPlan.find({ userId: req.user.id }).sort('-createdAt');

        res.status(200).json({
            success: true,
            count: nutritionPlans.length,
            data: nutritionPlans
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Get single nutrition plan
// @route   GET /api/nutrition/:id
// @access  Private
const getNutritionPlan = async (req, res, next) => {
    try {
        const nutritionPlan = await NutritionPlan.findById(req.params.id);

        if (!nutritionPlan) {
            return res.status(404).json({
                success: false,
                message: 'Nutrition plan not found'
            });
        }

        // Make sure user owns nutrition plan
        if (nutritionPlan.userId.toString() !== req.user.id) {
            return res.status(401).json({
                success: false,
                message: 'Not authorized to access this nutrition plan'
            });
        }

        res.status(200).json({
            success: true,
            data: nutritionPlan
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Update nutrition plan
// @route   PUT /api/nutrition/:id
// @access  Private
const updateNutritionPlan = async (req, res, next) => {
    try {
        let nutritionPlan = await NutritionPlan.findById(req.params.id);

        if (!nutritionPlan) {
            return res.status(404).json({
                success: false,
                message: 'Nutrition plan not found'
            });
        }

        // Make sure user owns nutrition plan
        if (nutritionPlan.userId.toString() !== req.user.id) {
            return res.status(401).json({
                success: false,
                message: 'Not authorized to update this nutrition plan'
            });
        }

        nutritionPlan = await NutritionPlan.findByIdAndUpdate(req.params.id, req.body, {
            new: true,
            runValidators: true
        });

        res.status(200).json({
            success: true,
            data: nutritionPlan
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Delete nutrition plan
// @route   DELETE /api/nutrition/:id
// @access  Private
const deleteNutritionPlan = async (req, res, next) => {
    try {
        const nutritionPlan = await NutritionPlan.findById(req.params.id);

        if (!nutritionPlan) {
            return res.status(404).json({
                success: false,
                message: 'Nutrition plan not found'
            });
        }

        // Make sure user owns nutrition plan
        if (nutritionPlan.userId.toString() !== req.user.id) {
            return res.status(401).json({
                success: false,
                message: 'Not authorized to delete this nutrition plan'
            });
        }

        await nutritionPlan.deleteOne();

        res.status(200).json({
            success: true,
            data: {}
        });
    } catch (error) {
        next(error);
    }
};

module.exports = {
    createNutritionPlan,
    getNutritionPlans,
    getNutritionPlan,
    updateNutritionPlan,
    deleteNutritionPlan
};

