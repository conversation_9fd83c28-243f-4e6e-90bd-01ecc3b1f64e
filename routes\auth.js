const express = require('express');
const {
    register,
    login,
    getUsers,
    getMe
} = require('../controllers/auth');

const { protect } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.post('/register', register);
router.post('/login', login);

// Protected routes
router.get('/', protect, getUsers);
router.get('/me', protect, getMe);

module.exports = router;

