import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Plus,
    Apple,
    Clock,
    ChevronDown,
    ChevronUp,
    Trash2,
    Flame,
    Zap,
    Droplets
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';
import api from '../config/api';

const Nutrition = () => {
    const [nutritionPlans, setNutritionPlans] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showAddForm, setShowAddForm] = useState(false);
    const [expandedPlan, setExpandedPlan] = useState(null);
    const [formData, setFormData] = useState({
        calories: '',
        protein: '',
        carbs: '',
        fat: '',
        meals: [{ name: '', time: '', foods: [{ name: '', quantity: '', calories: '' }], totalCalories: '' }]
    });

    useEffect(() => {
        fetchNutritionPlans();
    }, []);

    const fetchNutritionPlans = async () => {
        try {
            const response = await api.get('/nutrition');
            setNutritionPlans(response.data.data);
        } catch (error) {
            console.error('Error fetching nutrition plans:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleAddMeal = () => {
        setFormData({
            ...formData,
            meals: [...formData.meals, { name: '', time: '', foods: [{ name: '', quantity: '', calories: '' }], totalCalories: '' }]
        });
    };

    const handleAddFood = (mealIndex) => {
        const newMeals = [...formData.meals];
        newMeals[mealIndex].foods.push({ name: '', quantity: '', calories: '' });
        setFormData({ ...formData, meals: newMeals });
    };

    const handleMealChange = (mealIndex, field, value) => {
        const newMeals = [...formData.meals];
        newMeals[mealIndex][field] = value;
        setFormData({ ...formData, meals: newMeals });
    };

    const handleFoodChange = (mealIndex, foodIndex, field, value) => {
        const newMeals = [...formData.meals];
        newMeals[mealIndex].foods[foodIndex][field] = value;
        setFormData({ ...formData, meals: newMeals });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            await api.post('/nutrition', formData);
            setFormData({
                calories: '',
                protein: '',
                carbs: '',
                fat: '',
                meals: [{ name: '', time: '', foods: [{ name: '', quantity: '', calories: '' }], totalCalories: '' }]
            });
            setShowAddForm(false);
            fetchNutritionPlans();
        } catch (error) {
            console.error('Error creating nutrition plan:', error);
        }
    };

    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this nutrition plan?')) {
            try {
                await api.delete(`/nutrition/${id}`);
                fetchNutritionPlans();
            } catch (error) {
                console.error('Error deleting nutrition plan:', error);
            }
        }
    };

    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full animate-spin" />
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-between items-center mb-8"
                >
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Nutrition</h1>
                        <p className="text-gray-600 mt-2">Manage your nutrition plans</p>
                    </div>
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowAddForm(!showAddForm)}
                        className="btn-primary flex items-center space-x-2"
                    >
                        <Plus className="w-5 h-5" />
                        <span>Add Plan</span>
                    </motion.button>
                </motion.div>

                {/* Add Nutrition Plan Form */}
                <AnimatePresence>
                    {showAddForm && (
                        <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="card mb-8"
                        >
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create Nutrition Plan</h3>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Daily Targets */}
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Daily Calories
                                        </label>
                                        <input
                                            type="number"
                                            value={formData.calories}
                                            onChange={(e) => setFormData({ ...formData, calories: e.target.value })}
                                            className="input-field"
                                            placeholder="2000"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Protein (g)
                                        </label>
                                        <input
                                            type="number"
                                            value={formData.protein}
                                            onChange={(e) => setFormData({ ...formData, protein: e.target.value })}
                                            className="input-field"
                                            placeholder="150"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Carbs (g)
                                        </label>
                                        <input
                                            type="number"
                                            value={formData.carbs}
                                            onChange={(e) => setFormData({ ...formData, carbs: e.target.value })}
                                            className="input-field"
                                            placeholder="200"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Fat (g)
                                        </label>
                                        <input
                                            type="number"
                                            value={formData.fat}
                                            onChange={(e) => setFormData({ ...formData, fat: e.target.value })}
                                            className="input-field"
                                            placeholder="70"
                                            required
                                        />
                                    </div>
                                </div>

                                {/* Meals */}
                                <div className="space-y-4">
                                    <h4 className="font-medium text-gray-900">Meals</h4>
                                    {formData.meals.map((meal, mealIndex) => (
                                        <motion.div
                                            key={mealIndex}
                                            initial={{ opacity: 0, x: -20 }}
                                            animate={{ opacity: 1, x: 0 }}
                                            className="p-4 border border-gray-200 rounded-lg"
                                        >
                                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Meal Name
                                                    </label>
                                                    <input
                                                        type="text"
                                                        value={meal.name}
                                                        onChange={(e) => handleMealChange(mealIndex, 'name', e.target.value)}
                                                        className="input-field"
                                                        placeholder="e.g., Breakfast"
                                                        required
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Time
                                                    </label>
                                                    <input
                                                        type="text"
                                                        value={meal.time}
                                                        onChange={(e) => handleMealChange(mealIndex, 'time', e.target.value)}
                                                        className="input-field"
                                                        placeholder="08:00"
                                                        required
                                                    />
                                                </div>
                                                <div>
                                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                                        Total Calories
                                                    </label>
                                                    <input
                                                        type="number"
                                                        value={meal.totalCalories}
                                                        onChange={(e) => handleMealChange(mealIndex, 'totalCalories', e.target.value)}
                                                        className="input-field"
                                                        placeholder="500"
                                                        required
                                                    />
                                                </div>
                                            </div>

                                            {/* Foods */}
                                            <div className="space-y-3">
                                                <h5 className="font-medium text-gray-900">Foods</h5>
                                                {meal.foods.map((food, foodIndex) => (
                                                    <div key={foodIndex} className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                        <input
                                                            type="text"
                                                            value={food.name}
                                                            onChange={(e) => handleFoodChange(mealIndex, foodIndex, 'name', e.target.value)}
                                                            className="input-field"
                                                            placeholder="Food name"
                                                            required
                                                        />
                                                        <input
                                                            type="text"
                                                            value={food.quantity}
                                                            onChange={(e) => handleFoodChange(mealIndex, foodIndex, 'quantity', e.target.value)}
                                                            className="input-field"
                                                            placeholder="Quantity"
                                                            required
                                                        />
                                                        <input
                                                            type="number"
                                                            value={food.calories}
                                                            onChange={(e) => handleFoodChange(mealIndex, foodIndex, 'calories', e.target.value)}
                                                            className="input-field"
                                                            placeholder="Calories"
                                                            required
                                                        />
                                                    </div>
                                                ))}
                                                <button
                                                    type="button"
                                                    onClick={() => handleAddFood(mealIndex)}
                                                    className="btn-secondary text-sm"
                                                >
                                                    Add Food
                                                </button>
                                            </div>
                                        </motion.div>
                                    ))}
                                    <button
                                        type="button"
                                        onClick={handleAddMeal}
                                        className="btn-secondary flex items-center space-x-2"
                                    >
                                        <Plus className="w-4 h-4" />
                                        <span>Add Meal</span>
                                    </button>
                                </div>

                                <div className="flex space-x-4">
                                    <button type="submit" className="btn-primary">
                                        Create Plan
                                    </button>
                                </div>
                            </form>
                        </motion.div>
                    )}
                </AnimatePresence>

                {/* Nutrition Plans List */}
                <div className="space-y-4">
                    {nutritionPlans.length === 0 ? (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="text-center py-12"
                        >
                            <Apple className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No nutrition plans yet</h3>
                            <p className="text-gray-600">Create your first nutrition plan to get started!</p>
                        </motion.div>
                    ) : (
                        nutritionPlans.map((plan, index) => {
                            const macroData = [
                                { name: 'Protein', value: plan.protein, color: '#0088FE' },
                                { name: 'Carbs', value: plan.carbs, color: '#00C49F' },
                                { name: 'Fat', value: plan.fat, color: '#FFBB28' },
                            ];

                            return (
                                <motion.div
                                    key={plan._id}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: index * 0.1 }}
                                    className="card"
                                >
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center space-x-4">
                                            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                                <Apple className="w-6 h-6 text-green-600" />
                                            </div>
                                            <div>
                                                <h3 className="text-lg font-semibold text-gray-900">
                                                    {plan.calories} Calories Plan
                                                </h3>
                                                <p className="text-sm text-gray-600">
                                                    Created {new Date(plan.createdAt).toLocaleDateString()}
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <button
                                                onClick={() => setExpandedPlan(expandedPlan === plan._id ? null : plan._id)}
                                                className="p-2 text-gray-400 hover:text-gray-600"
                                            >
                                                {expandedPlan === plan._id ? (
                                                    <ChevronUp className="w-5 h-5" />
                                                ) : (
                                                    <ChevronDown className="w-5 h-5" />
                                                )}
                                            </button>
                                            <button
                                                onClick={() => handleDelete(plan._id)}
                                                className="p-2 text-red-400 hover:text-red-600"
                                            >
                                                <Trash2 className="w-5 h-5" />
                                            </button>
                                        </div>
                                    </div>

                                    {/* Expanded Plan Details */}
                                    <AnimatePresence>
                                        {expandedPlan === plan._id && (
                                            <motion.div
                                                initial={{ opacity: 0, height: 0 }}
                                                animate={{ opacity: 1, height: 'auto' }}
                                                exit={{ opacity: 0, height: 0 }}
                                                className="mt-4 pt-4 border-t border-gray-200"
                                            >
                                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                                    {/* Macros Chart */}
                                                    <div>
                                                        <h4 className="font-medium text-gray-900 mb-4">Macronutrients</h4>
                                                        <div className="h-64">
                                                            <ResponsiveContainer width="100%" height="100%">
                                                                <PieChart>
                                                                    <Pie
                                                                        data={macroData}
                                                                        cx="50%"
                                                                        cy="50%"
                                                                        labelLine={false}
                                                                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                                                        outerRadius={80}
                                                                        fill="#8884d8"
                                                                        dataKey="value"
                                                                    >
                                                                        {macroData.map((entry, index) => (
                                                                            <Cell key={`cell-${index}`} fill={entry.color} />
                                                                        ))}
                                                                    </Pie>
                                                                    <Tooltip />
                                                                </PieChart>
                                                            </ResponsiveContainer>
                                                        </div>
                                                        <div className="grid grid-cols-3 gap-4 mt-4">
                                                            <div className="text-center">
                                                                <div className="flex items-center justify-center space-x-2 mb-1">
                                                                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                                                                    <span className="text-sm font-medium">Protein</span>
                                                                </div>
                                                                <p className="text-lg font-bold text-gray-900">{plan.protein}g</p>
                                                            </div>
                                                            <div className="text-center">
                                                                <div className="flex items-center justify-center space-x-2 mb-1">
                                                                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                                                                    <span className="text-sm font-medium">Carbs</span>
                                                                </div>
                                                                <p className="text-lg font-bold text-gray-900">{plan.carbs}g</p>
                                                            </div>
                                                            <div className="text-center">
                                                                <div className="flex items-center justify-center space-x-2 mb-1">
                                                                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                                                    <span className="text-sm font-medium">Fat</span>
                                                                </div>
                                                                <p className="text-lg font-bold text-gray-900">{plan.fat}g</p>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    {/* Meals */}
                                                    <div>
                                                        <h4 className="font-medium text-gray-900 mb-4">Meals</h4>
                                                        <div className="space-y-3">
                                                            {plan.meals.map((meal, mealIndex) => (
                                                                <div key={mealIndex} className="p-3 bg-gray-50 rounded-lg">
                                                                    <div className="flex justify-between items-center mb-2">
                                                                        <h5 className="font-medium text-gray-900">{meal.name}</h5>
                                                                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                                                                            <Clock className="w-4 h-4" />
                                                                            <span>{meal.time}</span>
                                                                        </div>
                                                                    </div>
                                                                    <div className="space-y-2">
                                                                        {meal.foods.map((food, foodIndex) => (
                                                                            <div key={foodIndex} className="flex justify-between text-sm">
                                                                                <span className="text-gray-700">{food.name}</span>
                                                                                <span className="text-gray-600">{food.quantity} • {food.calories} cal</span>
                                                                            </div>
                                                                        ))}
                                                                    </div>
                                                                    <div className="mt-2 pt-2 border-t border-gray-200">
                                                                        <div className="flex justify-between items-center">
                                                                            <span className="text-sm font-medium text-gray-900">Total</span>
                                                                            <span className="text-sm font-bold text-gray-900">{meal.totalCalories} calories</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                </div>
                                            </motion.div>
                                        )}
                                    </AnimatePresence>
                                </motion.div>
                            );
                        })
                    )}
                </div>
            </div>
        </div>
    );
};

export default Nutrition;

