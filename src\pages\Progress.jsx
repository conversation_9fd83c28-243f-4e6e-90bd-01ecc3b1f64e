import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Plus,
    TrendingUp,
    Scale,
    ChevronDown,
    ChevronUp,
    Trash2,
    Calendar,
    Target
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import api from '../config/api';

const Progress = () => {
    const [progressEntries, setProgressEntries] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showAddForm, setShowAddForm] = useState(false);
    const [expandedEntry, setExpandedEntry] = useState(null);
    const [formData, setFormData] = useState({
        weight: '',
        bodyFat: '',
        notes: ''
    });

    useEffect(() => {
        fetchProgress();
    }, []);

    const fetchProgress = async () => {
        try {
            const response = await api.get('/progress');
            setProgressEntries(response.data.data);
        } catch (error) {
            console.error('Error fetching progress:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            await api.post('/progress', formData);
            setFormData({ weight: '', bodyFat: '', notes: '' });
            setShowAddForm(false);
            fetchProgress();
        } catch (error) {
            console.error('Error creating progress entry:', error);
        }
    };

    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this progress entry?')) {
            try {
                await api.delete(`/progress/${id}`);
                fetchProgress();
            } catch (error) {
                console.error('Error deleting progress entry:', error);
            }
        }
    };

    // Prepare chart data
    const chartData = progressEntries
        .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
        .map(entry => ({
            date: new Date(entry.createdAt).toLocaleDateString(),
            weight: entry.weight,
            bodyFat: entry.bodyFat || 0
        }));

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full animate-spin" />
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-between items-center mb-8"
                >
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Progress Tracking</h1>
                        <p className="text-gray-600 mt-2">Monitor your fitness journey</p>
                    </div>
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowAddForm(!showAddForm)}
                        className="btn-primary flex items-center space-x-2"
                    >
                        <Plus className="w-5 h-5" />
                        <span>Add Entry</span>
                    </motion.button>
                </motion.div>

                {/* Add Progress Form */}
                <AnimatePresence>
                    {showAddForm && (
                        <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="card mb-8"
                        >
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Add Progress Entry</h3>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Weight (kg)
                                        </label>
                                        <input
                                            type="number"
                                            value={formData.weight}
                                            onChange={(e) => setFormData({ ...formData, weight: e.target.value })}
                                            className="input-field"
                                            placeholder="75.5"
                                            step="0.1"
                                            min="20"
                                            max="300"
                                            required
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Body Fat % (optional)
                                        </label>
                                        <input
                                            type="number"
                                            value={formData.bodyFat}
                                            onChange={(e) => setFormData({ ...formData, bodyFat: e.target.value })}
                                            className="input-field"
                                            placeholder="15.5"
                                            step="0.1"
                                            min="0"
                                            max="50"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Notes (optional)
                                    </label>
                                    <textarea
                                        value={formData.notes}
                                        onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                                        className="input-field"
                                        rows="3"
                                        placeholder="How are you feeling? Any changes in routine?"
                                    />
                                </div>
                                <div className="flex space-x-4">
                                    <button type="submit" className="btn-primary">
                                        Add Entry
                                    </button>
                                </div>
                            </form>
                        </motion.div>
                    )}
                </AnimatePresence>

                {/* Progress Chart */}
                {progressEntries.length > 0 && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="card mb-8"
                    >
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Weight Progress</h3>
                        <div className="h-64">
                            <ResponsiveContainer width="100%" height="100%">
                                <LineChart data={chartData}>
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis dataKey="date" />
                                    <YAxis />
                                    <Tooltip />
                                    <Line
                                        type="monotone"
                                        dataKey="weight"
                                        stroke="#0ea5e9"
                                        strokeWidth={2}
                                        dot={{ fill: '#0ea5e9', strokeWidth: 2, r: 4 }}
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        </div>
                    </motion.div>
                )}

                {/* Progress Stats */}
                {progressEntries.length > 0 && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
                    >
                        <div className="card">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Current Weight</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {progressEntries[0]?.weight} kg
                                    </p>
                                </div>
                                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <Scale className="w-6 h-6 text-blue-600" />
                                </div>
                            </div>
                        </div>
                        <div className="card">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Total Entries</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {progressEntries.length}
                                    </p>
                                </div>
                                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <Target className="w-6 h-6 text-green-600" />
                                </div>
                            </div>
                        </div>
                        <div className="card">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm font-medium text-gray-600">Latest Body Fat</p>
                                    <p className="text-2xl font-bold text-gray-900">
                                        {progressEntries[0]?.bodyFat ? `${progressEntries[0].bodyFat}%` : 'N/A'}
                                    </p>
                                </div>
                                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <TrendingUp className="w-6 h-6 text-purple-600" />
                                </div>
                            </div>
                        </div>
                    </motion.div>
                )}

                {/* Progress Entries List */}
                <div className="space-y-4">
                    {progressEntries.length === 0 ? (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="text-center py-12"
                        >
                            <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No progress entries yet</h3>
                            <p className="text-gray-600">Add your first progress entry to start tracking!</p>
                        </motion.div>
                    ) : (
                        progressEntries.map((entry, index) => (
                            <motion.div
                                key={entry._id}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="card"
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-4">
                                        <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                            <Scale className="w-6 h-6 text-primary-600" />
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-semibold text-gray-900">
                                                {entry.weight} kg
                                            </h3>
                                            <p className="text-sm text-gray-600">
                                                {new Date(entry.createdAt).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <button
                                            onClick={() => setExpandedEntry(expandedEntry === entry._id ? null : entry._id)}
                                            className="p-2 text-gray-400 hover:text-gray-600"
                                        >
                                            {expandedEntry === entry._id ? (
                                                <ChevronUp className="w-5 h-5" />
                                            ) : (
                                                <ChevronDown className="w-5 h-5" />
                                            )}
                                        </button>
                                        <button
                                            onClick={() => handleDelete(entry._id)}
                                            className="p-2 text-red-400 hover:text-red-600"
                                        >
                                            <Trash2 className="w-5 h-5" />
                                        </button>
                                    </div>
                                </div>

                                {/* Expanded Entry Details */}
                                <AnimatePresence>
                                    {expandedEntry === entry._id && (
                                        <motion.div
                                            initial={{ opacity: 0, height: 0 }}
                                            animate={{ opacity: 1, height: 'auto' }}
                                            exit={{ opacity: 0, height: 0 }}
                                            className="mt-4 pt-4 border-t border-gray-200"
                                        >
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                                <div>
                                                    <h4 className="font-medium text-gray-900 mb-3">Measurements</h4>
                                                    <div className="space-y-3">
                                                        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                                            <span className="text-sm font-medium text-gray-700">Weight</span>
                                                            <span className="text-lg font-bold text-gray-900">{entry.weight} kg</span>
                                                        </div>
                                                        {entry.bodyFat && (
                                                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                                                <span className="text-sm font-medium text-gray-700">Body Fat</span>
                                                                <span className="text-lg font-bold text-gray-900">{entry.bodyFat}%</span>
                                                            </div>
                                                        )}
                                                    </div>
                                                </div>
                                                <div>
                                                    <h4 className="font-medium text-gray-900 mb-3">Notes</h4>
                                                    {entry.notes ? (
                                                        <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                                                            {entry.notes}
                                                        </p>
                                                    ) : (
                                                        <p className="text-gray-500 italic">No notes added</p>
                                                    )}
                                                </div>
                                            </div>
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </motion.div>
                        ))
                    )}
                </div>
            </div>
        </div>
    );
};

export default Progress;

