{"info": {"name": "FitVerse API", "description": "Complete API collection for FitVerse fitness tracking application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "Welcome", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/", "host": ["{{baseUrl}}"], "path": [""]}}}, {"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"goal\": \"loss\",\n  \"weight\": 80\n}"}, "url": {"raw": "{{baseUrl}}/api/users/register", "host": ["{{baseUrl}}"], "path": ["api", "users", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    pm.collectionVariables.set('token', pm.response.json().token);", "}"]}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/login", "host": ["{{baseUrl}}"], "path": ["api", "users", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    pm.collectionVariables.set('token', pm.response.json().token);", "}"]}}]}, {"name": "Get Current User", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/me", "host": ["{{baseUrl}}"], "path": ["api", "users", "me"]}}}, {"name": "Get All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}}]}, {"name": "Workouts", "item": [{"name": "Create Workout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"exercises\": [\n    {\n      \"name\": \"Push-ups\",\n      \"sets\": 3,\n      \"reps\": 15,\n      \"instructions\": \"Keep your body straight\"\n    },\n    {\n      \"name\": \"Squats\",\n      \"sets\": 4,\n      \"reps\": 20,\n      \"instructions\": \"Keep your back straight\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/workouts", "host": ["{{baseUrl}}"], "path": ["api", "workouts"]}}}, {"name": "Get All Workouts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/workouts", "host": ["{{baseUrl}}"], "path": ["api", "workouts"]}}}, {"name": "Get Single Workout", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/workouts/:id", "host": ["{{baseUrl}}"], "path": ["api", "workouts", ":id"], "variable": [{"key": "id", "value": "workout_id_here"}]}}}, {"name": "Update Workout", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"exercises\": [\n    {\n      \"name\": \"Push-ups\",\n      \"sets\": 4,\n      \"reps\": 20,\n      \"instructions\": \"Keep your body straight and go deeper\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/workouts/:id", "host": ["{{baseUrl}}"], "path": ["api", "workouts", ":id"], "variable": [{"key": "id", "value": "workout_id_here"}]}}}, {"name": "Delete Workout", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/workouts/:id", "host": ["{{baseUrl}}"], "path": ["api", "workouts", ":id"], "variable": [{"key": "id", "value": "workout_id_here"}]}}}]}, {"name": "Nutrition", "item": [{"name": "Create Nutrition Plan", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"calories\": 2000,\n  \"protein\": 150,\n  \"carbs\": 200,\n  \"fat\": 70,\n  \"meals\": [\n    {\n      \"name\": \"Breakfast\",\n      \"time\": \"08:00\",\n      \"foods\": [\n        {\n          \"name\": \"Oatmeal\",\n          \"quantity\": \"1 cup\",\n          \"calories\": 300\n        }\n      ],\n      \"totalCalories\": 300\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/api/nutrition", "host": ["{{baseUrl}}"], "path": ["api", "nutrition"]}}}, {"name": "Get All Nutrition Plans", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/nutrition", "host": ["{{baseUrl}}"], "path": ["api", "nutrition"]}}}, {"name": "Get Single Nutrition Plan", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/nutrition/:id", "host": ["{{baseUrl}}"], "path": ["api", "nutrition", ":id"], "variable": [{"key": "id", "value": "nutrition_plan_id_here"}]}}}, {"name": "Update Nutrition Plan", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"calories\": 2200,\n  \"protein\": 160,\n  \"carbs\": 220,\n  \"fat\": 75\n}"}, "url": {"raw": "{{baseUrl}}/api/nutrition/:id", "host": ["{{baseUrl}}"], "path": ["api", "nutrition", ":id"], "variable": [{"key": "id", "value": "nutrition_plan_id_here"}]}}}, {"name": "Delete Nutrition Plan", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/nutrition/:id", "host": ["{{baseUrl}}"], "path": ["api", "nutrition", ":id"], "variable": [{"key": "id", "value": "nutrition_plan_id_here"}]}}}]}, {"name": "Progress", "item": [{"name": "Create Progress Entry", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"weight\": 78.5,\n  \"bodyFat\": 15.2,\n  \"notes\": \"Feeling great after this week's workouts\"\n}"}, "url": {"raw": "{{baseUrl}}/api/progress", "host": ["{{baseUrl}}"], "path": ["api", "progress"]}}}, {"name": "Get All Progress Entries", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/progress", "host": ["{{baseUrl}}"], "path": ["api", "progress"]}}}, {"name": "Get Progress Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/progress/stats", "host": ["{{baseUrl}}"], "path": ["api", "progress", "stats"]}}}, {"name": "Get Single Progress Entry", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/progress/:id", "host": ["{{baseUrl}}"], "path": ["api", "progress", ":id"], "variable": [{"key": "id", "value": "progress_entry_id_here"}]}}}, {"name": "Update Progress Entry", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"weight\": 78.0,\n  \"bodyFat\": 14.8,\n  \"notes\": \"Updated measurements - making good progress!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/progress/:id", "host": ["{{baseUrl}}"], "path": ["api", "progress", ":id"], "variable": [{"key": "id", "value": "progress_entry_id_here"}]}}}, {"name": "Delete Progress Entry", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/progress/:id", "host": ["{{baseUrl}}"], "path": ["api", "progress", ":id"], "variable": [{"key": "id", "value": "progress_entry_id_here"}]}}}]}]}