const Workout = require('../models/Workout');

// @desc    Create new workout
// @route   POST /api/workouts
// @access  Private
const createWorkout = async (req, res, next) => {
    try {
        // Add user to req.body
        req.body.userId = req.user.id;

        const workout = await Workout.create(req.body);

        res.status(201).json({
            success: true,
            data: workout
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Get all workouts for a user
// @route   GET /api/workouts
// @access  Private
const getWorkouts = async (req, res, next) => {
    try {
        const workouts = await Workout.find({ userId: req.user.id }).sort('-createdAt');

        res.status(200).json({
            success: true,
            count: workouts.length,
            data: workouts
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Get single workout
// @route   GET /api/workouts/:id
// @access  Private
const getWorkout = async (req, res, next) => {
    try {
        const workout = await Workout.findById(req.params.id);

        if (!workout) {
            return res.status(404).json({
                success: false,
                message: 'Workout not found'
            });
        }

        // Make sure user owns workout
        if (workout.userId.toString() !== req.user.id) {
            return res.status(401).json({
                success: false,
                message: 'Not authorized to access this workout'
            });
        }

        res.status(200).json({
            success: true,
            data: workout
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Update workout
// @route   PUT /api/workouts/:id
// @access  Private
const updateWorkout = async (req, res, next) => {
    try {
        let workout = await Workout.findById(req.params.id);

        if (!workout) {
            return res.status(404).json({
                success: false,
                message: 'Workout not found'
            });
        }

        // Make sure user owns workout
        if (workout.userId.toString() !== req.user.id) {
            return res.status(401).json({
                success: false,
                message: 'Not authorized to update this workout'
            });
        }

        workout = await Workout.findByIdAndUpdate(req.params.id, req.body, {
            new: true,
            runValidators: true
        });

        res.status(200).json({
            success: true,
            data: workout
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Delete workout
// @route   DELETE /api/workouts/:id
// @access  Private
const deleteWorkout = async (req, res, next) => {
    try {
        const workout = await Workout.findById(req.params.id);

        if (!workout) {
            return res.status(404).json({
                success: false,
                message: 'Workout not found'
            });
        }

        // Make sure user owns workout
        if (workout.userId.toString() !== req.user.id) {
            return res.status(401).json({
                success: false,
                message: 'Not authorized to delete this workout'
            });
        }

        await workout.deleteOne();

        res.status(200).json({
            success: true,
            data: {}
        });
    } catch (error) {
        next(error);
    }
};

module.exports = {
    createWorkout,
    getWorkouts,
    getWorkout,
    updateWorkout,
    deleteWorkout
};

