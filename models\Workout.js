const mongoose = require('mongoose');

const ExerciseSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Please add exercise name'],
        trim: true,
        maxlength: [100, 'Exercise name cannot be more than 100 characters']
    },
    sets: {
        type: Number,
        required: [true, 'Please add number of sets'],
        min: [1, 'Sets must be at least 1'],
        max: [50, 'Sets cannot exceed 50']
    },
    reps: {
        type: Number,
        required: [true, 'Please add number of reps'],
        min: [1, 'Reps must be at least 1'],
        max: [1000, 'Reps cannot exceed 1000']
    },
    instructions: {
        type: String,
        maxlength: [500, 'Instructions cannot be more than 500 characters']
    }
});

const WorkoutSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
    },
    exercises: [ExerciseSchema],
    createdAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = mongoose.model('Workout', WorkoutSchema);

