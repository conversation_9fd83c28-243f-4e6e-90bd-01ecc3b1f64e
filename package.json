{"name": "fitverse-fullstack", "private": true, "version": "1.0.0", "type": "commonjs", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "start-full": "node start.js", "server": "node server.js", "client": "cd fitverse-frontend && npm run dev", "build": "cd fitverse-frontend && npm run build", "install-all": "npm install && cd fitverse-frontend && npm install", "start": "node server.js", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "cd fitverse-frontend && npm run preview"}, "dependencies": {"axios": "^1.6.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "framer-motion": "^10.16.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.292.0", "mongoose": "^7.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "recharts": "^2.8.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.5.0"}}