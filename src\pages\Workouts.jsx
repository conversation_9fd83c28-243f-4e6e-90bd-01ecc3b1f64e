import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Plus,
    <PERSON><PERSON><PERSON>,
    Clock,
    ChevronDown,
    ChevronUp,
    Edit,
    Trash2,
    Calendar
} from 'lucide-react';
import api from '../config/api';

const Workouts = () => {
    const [workouts, setWorkouts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [showAddForm, setShowAddForm] = useState(false);
    const [expandedWorkout, setExpandedWorkout] = useState(null);
    const [formData, setFormData] = useState({
        exercises: [{ name: '', sets: '', reps: '', instructions: '' }]
    });

    useEffect(() => {
        fetchWorkouts();
    }, []);

    const fetchWorkouts = async () => {
        try {
            const response = await api.get('/workouts');
            setWorkouts(response.data.data);
        } catch (error) {
            console.error('Error fetching workouts:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleAddExercise = () => {
        setFormData({
            ...formData,
            exercises: [...formData.exercises, { name: '', sets: '', reps: '', instructions: '' }]
        });
    };

    const handleExerciseChange = (index, field, value) => {
        const newExercises = [...formData.exercises];
        newExercises[index][field] = value;
        setFormData({ ...formData, exercises: newExercises });
    };

    const handleRemoveExercise = (index) => {
        const newExercises = formData.exercises.filter((_, i) => i !== index);
        setFormData({ ...formData, exercises: newExercises });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            await api.post('/workouts', formData);
            setFormData({ exercises: [{ name: '', sets: '', reps: '', instructions: '' }] });
            setShowAddForm(false);
            fetchWorkouts();
        } catch (error) {
            console.error('Error creating workout:', error);
        }
    };

    const handleDelete = async (id) => {
        if (window.confirm('Are you sure you want to delete this workout?')) {
            try {
                await api.delete(`/workouts/${id}`);
                fetchWorkouts();
            } catch (error) {
                console.error('Error deleting workout:', error);
            }
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full animate-spin" />
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="flex justify-between items-center mb-8"
                >
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">Workouts</h1>
                        <p className="text-gray-600 mt-2">Manage your workout routines</p>
                    </div>
                    <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setShowAddForm(!showAddForm)}
                        className="btn-primary flex items-center space-x-2"
                    >
                        <Plus className="w-5 h-5" />
                        <span>Add Workout</span>
                    </motion.button>
                </motion.div>

                {/* Add Workout Form */}
                <AnimatePresence>
                    {showAddForm && (
                        <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="card mb-8"
                        >
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Workout</h3>
                            <form onSubmit={handleSubmit} className="space-y-6">
                                {formData.exercises.map((exercise, index) => (
                                    <motion.div
                                        key={index}
                                        initial={{ opacity: 0, x: -20 }}
                                        animate={{ opacity: 1, x: 0 }}
                                        className="p-4 border border-gray-200 rounded-lg"
                                    >
                                        <div className="flex justify-between items-center mb-3">
                                            <h4 className="font-medium text-gray-900">Exercise {index + 1}</h4>
                                            {formData.exercises.length > 1 && (
                                                <button
                                                    type="button"
                                                    onClick={() => handleRemoveExercise(index)}
                                                    className="text-red-600 hover:text-red-700"
                                                >
                                                    <Trash2 className="w-4 h-4" />
                                                </button>
                                            )}
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Exercise Name
                                                </label>
                                                <input
                                                    type="text"
                                                    value={exercise.name}
                                                    onChange={(e) => handleExerciseChange(index, 'name', e.target.value)}
                                                    className="input-field"
                                                    placeholder="e.g., Push-ups"
                                                    required
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Sets
                                                </label>
                                                <input
                                                    type="number"
                                                    value={exercise.sets}
                                                    onChange={(e) => handleExerciseChange(index, 'sets', e.target.value)}
                                                    className="input-field"
                                                    placeholder="3"
                                                    min="1"
                                                    required
                                                />
                                            </div>
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                                    Reps
                                                </label>
                                                <input
                                                    type="number"
                                                    value={exercise.reps}
                                                    onChange={(e) => handleExerciseChange(index, 'reps', e.target.value)}
                                                    className="input-field"
                                                    placeholder="12"
                                                    min="1"
                                                    required
                                                />
                                            </div>
                                        </div>
                                        <div className="mt-3">
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Instructions
                                            </label>
                                            <textarea
                                                value={exercise.instructions}
                                                onChange={(e) => handleExerciseChange(index, 'instructions', e.target.value)}
                                                className="input-field"
                                                rows="2"
                                                placeholder="Optional instructions..."
                                            />
                                        </div>
                                    </motion.div>
                                ))}
                                <div className="flex space-x-4">
                                    <button
                                        type="button"
                                        onClick={handleAddExercise}
                                        className="btn-secondary flex items-center space-x-2"
                                    >
                                        <Plus className="w-4 h-4" />
                                        <span>Add Exercise</span>
                                    </button>
                                    <button type="submit" className="btn-primary">
                                        Create Workout
                                    </button>
                                </div>
                            </form>
                        </motion.div>
                    )}
                </AnimatePresence>

                {/* Workouts List */}
                <div className="space-y-4">
                    {workouts.length === 0 ? (
                        <motion.div
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            className="text-center py-12"
                        >
                            <Dumbbell className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-medium text-gray-900 mb-2">No workouts yet</h3>
                            <p className="text-gray-600">Create your first workout to get started!</p>
                        </motion.div>
                    ) : (
                        workouts.map((workout, index) => (
                            <motion.div
                                key={workout._id}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="card"
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-4">
                                        <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                            <Dumbbell className="w-6 h-6 text-primary-600" />
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-semibold text-gray-900">
                                                Workout {workout.exercises.length} exercises
                                            </h3>
                                            <p className="text-sm text-gray-600">
                                                Created {new Date(workout.createdAt).toLocaleDateString()}
                                            </p>
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <button
                                            onClick={() => setExpandedWorkout(expandedWorkout === workout._id ? null : workout._id)}
                                            className="p-2 text-gray-400 hover:text-gray-600"
                                        >
                                            {expandedWorkout === workout._id ? (
                                                <ChevronUp className="w-5 h-5" />
                                            ) : (
                                                <ChevronDown className="w-5 h-5" />
                                            )}
                                        </button>
                                        <button
                                            onClick={() => handleDelete(workout._id)}
                                            className="p-2 text-red-400 hover:text-red-600"
                                        >
                                            <Trash2 className="w-5 h-5" />
                                        </button>
                                    </div>
                                </div>

                                {/* Expanded Workout Details */}
                                <AnimatePresence>
                                    {expandedWorkout === workout._id && (
                                        <motion.div
                                            initial={{ opacity: 0, height: 0 }}
                                            animate={{ opacity: 1, height: 'auto' }}
                                            exit={{ opacity: 0, height: 0 }}
                                            className="mt-4 pt-4 border-t border-gray-200"
                                        >
                                            <div className="space-y-3">
                                                {workout.exercises.map((exercise, exerciseIndex) => (
                                                    <div
                                                        key={exerciseIndex}
                                                        className="p-3 bg-gray-50 rounded-lg"
                                                    >
                                                        <div className="flex justify-between items-start">
                                                            <div className="flex-1">
                                                                <h4 className="font-medium text-gray-900">{exercise.name}</h4>
                                                                <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                                                                    <span className="flex items-center space-x-1">
                                                                        <Clock className="w-4 h-4" />
                                                                        <span>{exercise.sets} sets</span>
                                                                    </span>
                                                                    <span>•</span>
                                                                    <span>{exercise.reps} reps</span>
                                                                </div>
                                                                {exercise.instructions && (
                                                                    <p className="text-sm text-gray-600 mt-2">
                                                                        {exercise.instructions}
                                                                    </p>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </motion.div>
                                    )}
                                </AnimatePresence>
                            </motion.div>
                        ))
                    )}
                </div>
            </div>
        </div>
    );
};

export default Workouts;

