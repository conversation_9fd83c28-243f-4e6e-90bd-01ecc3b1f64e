const express = require('express');
const {
    createProgress,
    getProgress,
    getProgressEntry,
    updateProgress,
    deleteProgress,
    getProgressStats
} = require('../controllers/progress');

const { protect } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(protect);

router.route('/')
    .get(getProgress)
    .post(createProgress);

router.route('/stats')
    .get(getProgressStats);

router.route('/:id')
    .get(getProgressEntry)
    .put(updateProgress)
    .delete(deleteProgress);

module.exports = router;

