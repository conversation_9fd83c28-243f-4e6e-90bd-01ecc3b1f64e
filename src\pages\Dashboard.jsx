import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    TrendingUp,
    Target,
    Calendar,
    Clock,
    Activity
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import api from '../config/api';

const Dashboard = () => {
    const { user } = useAuth();
    const [stats, setStats] = useState({
        workouts: 0,
        nutritionPlans: 0,
        progressEntries: 0,
        latestWeight: user?.weight || 0,
    });
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchDashboardData();
    }, []);

    const fetchDashboardData = async () => {
        try {
            const [workoutsRes, nutritionRes, progressRes] = await Promise.all([
                api.get('/workouts'),
                api.get('/nutrition'),
                api.get('/progress'),
            ]);

            setStats({
                workouts: workoutsRes.data.count || 0,
                nutritionPlans: nutritionRes.data.count || 0,
                progressEntries: progressRes.data.count || 0,
                latestWeight: user?.weight || 0,
            });
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
        } finally {
            setLoading(false);
        }
    };

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.5,
            },
        },
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full animate-spin" />
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Hero Section */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="relative overflow-hidden bg-gradient-to-r from-primary-600 to-secondary-600 text-white"
            >
                <div className="absolute inset-0 bg-black opacity-20"></div>
                <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                    <div className="text-center">
                        <motion.h1
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.2 }}
                            className="text-4xl md:text-6xl font-bold mb-4"
                        >
                            Welcome back, {user?.name}!
                        </motion.h1>
                        <motion.p
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.4 }}
                            className="text-xl md:text-2xl opacity-90"
                        >
                            Ready to crush your fitness goals today?
                        </motion.p>
                    </div>
                </div>
            </motion.div>

            {/* Stats Cards */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-12 relative z-10">
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
                >
                    {/* Calories Card */}
                    <motion.div variants={itemVariants} className="card">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Daily Calories</p>
                                <p className="text-2xl font-bold text-gray-900">2,100</p>
                                <p className="text-sm text-green-600">+5% from yesterday</p>
                            </div>
                            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                <Flame className="w-6 h-6 text-orange-600" />
                            </div>
                        </div>
                    </motion.div>

                    {/* Workouts Card */}
                    <motion.div variants={itemVariants} className="card">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Workouts</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.workouts}</p>
                                <p className="text-sm text-blue-600">This month</p>
                            </div>
                            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <Dumbbell className="w-6 h-6 text-blue-600" />
                            </div>
                        </div>
                    </motion.div>

                    {/* Progress Card */}
                    <motion.div variants={itemVariants} className="card">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Current Weight</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.latestWeight} kg</p>
                                <p className="text-sm text-purple-600">-2.5 kg this month</p>
                            </div>
                            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <TrendingUp className="w-6 h-6 text-purple-600" />
                            </div>
                        </div>
                    </motion.div>

                    {/* Goal Card */}
                    <motion.div variants={itemVariants} className="card">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Fitness Goal</p>
                                <p className="text-2xl font-bold text-gray-900 capitalize">{user?.goal}</p>
                                <p className="text-sm text-green-600">On track</p>
                            </div>
                            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <Target className="w-6 h-6 text-green-600" />
                            </div>
                        </div>
                    </motion.div>
                </motion.div>

                {/* Quick Actions */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"
                >
                    {/* Today's Workout */}
                    <div className="card">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-gray-900">Today's Workout</h3>
                            <Calendar className="w-5 h-5 text-gray-400" />
                        </div>
                        <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                                        <Dumbbell className="w-4 h-4 text-primary-600" />
                                    </div>
                                    <div>
                                        <p className="font-medium text-gray-900">Upper Body</p>
                                        <p className="text-sm text-gray-600">6 exercises</p>
                                    </div>
                                </div>
                                <div className="text-right">
                                    <p className="text-sm font-medium text-gray-900">45 min</p>
                                    <p className="text-xs text-gray-500">Estimated</p>
                                </div>
                            </div>
                            <button className="w-full btn-primary">
                                Start Workout
                            </button>
                        </div>
                    </div>

                    {/* Nutrition Summary */}
                    <div className="card">
                        <div className="flex items-center justify-between mb-4">
                            <h3 className="text-lg font-semibold text-gray-900">Nutrition Today</h3>
                            <Activity className="w-5 h-5 text-gray-400" />
                        </div>
                        <div className="space-y-4">
                            <div className="grid grid-cols-3 gap-4">
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-blue-600">150g</p>
                                    <p className="text-xs text-gray-600">Protein</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-green-600">200g</p>
                                    <p className="text-xs text-gray-600">Carbs</p>
                                </div>
                                <div className="text-center">
                                    <p className="text-2xl font-bold text-orange-600">70g</p>
                                    <p className="text-xs text-gray-600">Fat</p>
                                </div>
                            </div>
                            <div className="bg-gray-50 rounded-lg p-3">
                                <div className="flex items-center justify-between text-sm">
                                    <span className="text-gray-600">Calories consumed</span>
                                    <span className="font-medium">1,850 / 2,100</span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                                    <div className="bg-primary-600 h-2 rounded-full" style={{ width: '88%' }}></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </motion.div>

                {/* Recent Activity */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 }}
                    className="card"
                >
                    <div className="flex items-center justify-between mb-6">
                        <h3 className="text-lg font-semibold text-gray-900">Recent Activity</h3>
                        <Clock className="w-5 h-5 text-gray-400" />
                    </div>
                    <div className="space-y-4">
                        <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                <Dumbbell className="w-5 h-5 text-green-600" />
                            </div>
                            <div className="flex-1">
                                <p className="font-medium text-gray-900">Completed Upper Body Workout</p>
                                <p className="text-sm text-gray-600">45 minutes • 2 hours ago</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <TrendingUp className="w-5 h-5 text-blue-600" />
                            </div>
                            <div className="flex-1">
                                <p className="font-medium text-gray-900">Updated Progress</p>
                                <p className="text-sm text-gray-600">Weight: 78.5kg • 1 day ago</p>
                            </div>
                        </div>
                        <div className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                <Activity className="w-5 h-5 text-purple-600" />
                            </div>
                            <div className="flex-1">
                                <p className="font-medium text-gray-900">Created Nutrition Plan</p>
                                <p className="text-sm text-gray-600">2,100 calories • 3 days ago</p>
                            </div>
                        </div>
                    </div>
                </motion.div>
            </div>
        </div>
    );
};

export default Dashboard;

