@echo off
echo 🚀 بدء تشغيل FitVerse...
echo.

REM التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت. يرجى تثبيت Node.js أولاً.
    pause
    exit /b 1
)

REM التحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير مثبت. يرجى تثبيت npm أولاً.
    pause
    exit /b 1
)

echo ✅ Node.js و npm مثبتان بنجاح
echo.

REM تثبيت التبعيات إذا لم تكن مثبتة
if not exist "node_modules" (
    echo 📦 تثبيت تبعيات الباك إند...
    npm install
)

if not exist "fitverse-frontend\node_modules" (
    echo 📦 تثبيت تبعيات الفرونت إند...
    cd fitverse-frontend
    npm install
    cd ..
)

echo.
echo 🎯 تشغيل المشروع...
echo.
echo 📱 الفرونت إند: http://localhost:3000
echo 🔌 الباك إند API: http://localhost:5000
echo.
echo اضغط Ctrl+C لإيقاف المشروع
echo.

REM تشغيل المشروع
npm run dev

pause
