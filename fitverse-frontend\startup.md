# FitVerse Frontend - Quick Start Guide

## 🚀 Quick Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure API
Update the API URL in `src/config/api.js`:
```javascript
const API_BASE_URL = 'http://localhost:5000/api';
```

### 3. Start Development Server
```bash
npm run dev
```

### 4. Open Browser
Navigate to: `http://localhost:5173`

## 📋 Prerequisites

- Node.js (v16 or higher)
- Backend API running on port 5000
- MongoDB running locally

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🌐 Environment Variables

Create a `.env` file in the root directory:
```env
VITE_API_URL=http://localhost:5000/api
```

## 📱 Features

- ✅ User Authentication (Login/Register)
- ✅ Dashboard with Stats
- ✅ Workout Management
- ✅ Nutrition Planning
- ✅ Progress Tracking
- ✅ Responsive Design
- ✅ Beautiful Animations
- ✅ Interactive Charts

## 🎯 Next Steps

1. Start the backend server
2. Register a new account
3. Explore the dashboard
4. Create your first workout
5. Plan your nutrition
6. Track your progress

---

**Happy Coding! 💪**

