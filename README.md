# FitVerse - Full Stack Fitness Application

تطبيق FitVerse هو تطبيق شامل لإدارة اللياقة البدنية يتضمن تمارين رياضية، خطط تغذية، ومتابعة التقدم.

## 🚀 التشغيل السريع

### المتطلبات الأساسية
- Node.js (v16 أو أحدث)
- npm أو yarn
- MongoDB (محلي أو Atlas)

### خطوات التشغيل

1. **تثبيت التبعيات**
```bash
npm run install-all
```

2. **إعداد ملف البيئة**
```bash
cp env.example .env
```
ثم قم بتعديل ملف `.env` بإعدادات قاعدة البيانات الخاصة بك.

3. **تشغيل المشروع كاملاً**
```bash
npm run dev
```

هذا الأمر سيشغل:
- الباك إند على المنفذ 5000
- الفرونت إند على المنفذ 3000

## 📁 بنية المشروع

```
Gym/
├── config/                 # إعدادات قاعدة البيانات
├── controllers/            # منطق الأعمال
├── middleware/             # الوسائط البرمجية
├── models/                 # نماذج البيانات
├── routes/                 # مسارات API
├── src/                    # الفرونت إند (React)
│   ├── components/         # المكونات
│   ├── pages/             # الصفحات
│   ├── context/           # سياق التطبيق
│   └── config/            # إعدادات الفرونت إند
├── fitverse-frontend/      # مجلد الفرونت إند المنفصل
├── server.js              # نقطة بداية الباك إند
└── package.json           # إعدادات المشروع الرئيسي
```

## 🔧 الأوامر المتاحة

- `npm run dev` - تشغيل الباك إند والفرونت إند معاً
- `npm run server` - تشغيل الباك إند فقط
- `npm run client` - تشغيل الفرونت إند فقط
- `npm run build` - بناء الفرونت إند للإنتاج
- `npm run install-all` - تثبيت جميع التبعيات

## 🌐 الوصول للتطبيق

- **الفرونت إند**: http://localhost:3000
- **الباك إند API**: http://localhost:5000
- **توثيق API**: http://localhost:5000

## 🔌 نقاط النهاية API

- `POST /api/users/register` - تسجيل مستخدم جديد
- `POST /api/users/login` - تسجيل الدخول
- `GET /api/workouts` - جلب التمارين
- `POST /api/workouts` - إنشاء تمرين جديد
- `GET /api/nutrition` - جلب خطط التغذية
- `POST /api/nutrition` - إنشاء خطة تغذية
- `GET /api/progress` - جلب التقدم
- `POST /api/progress` - تحديث التقدم

## 🛠️ التقنيات المستخدمة

### الباك إند
- Node.js
- Express.js
- MongoDB
- Mongoose
- JWT Authentication
- bcryptjs

### الفرونت إند
- React
- Vite
- Tailwind CSS
- Axios
- React Router
- Framer Motion
- Recharts

## 📝 ملاحظات التطوير

- يستخدم المشروع البروكسي في وضع التطوير لتجنب مشاكل CORS
- يتم تخزين التوكن في localStorage
- جميع الطلبات للـ API تتضمن التوكن تلقائياً
- في حالة انتهاء صلاحية التوكن، يتم توجيه المستخدم لصفحة تسجيل الدخول
