// Test script to verify API connection
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000';

async function testAPIConnection() {
    console.log('🔍 Testing API Connection...\n');
    
    try {
        // Test 1: Check if server is running
        console.log('1. Testing server status...');
        const response = await axios.get(`${API_BASE_URL}/`);
        console.log('✅ Server is running');
        console.log('📋 Server Info:', response.data);
        console.log('');
        
        // Test 2: Test user registration endpoint
        console.log('2. Testing user registration endpoint...');
        const testUser = {
            name: 'Test User',
            email: '<EMAIL>',
            password: 'password123',
            age: 25,
            gender: 'male',
            weight: 70,
            height: 175,
            fitnessLevel: 'beginner',
            goals: ['weight_loss']
        };
        
        try {
            const registerResponse = await axios.post(`${API_BASE_URL}/api/users/register`, testUser);
            console.log('✅ Registration endpoint working');
            console.log('🔑 Token received:', registerResponse.data.token ? 'Yes' : 'No');
        } catch (error) {
            if (error.response?.status === 400 && error.response?.data?.message?.includes('already exists')) {
                console.log('✅ Registration endpoint working (user already exists)');
            } else {
                console.log('❌ Registration failed:', error.response?.data?.message || error.message);
            }
        }
        console.log('');
        
        // Test 3: Test login endpoint
        console.log('3. Testing login endpoint...');
        try {
            const loginResponse = await axios.post(`${API_BASE_URL}/api/users/login`, {
                email: '<EMAIL>',
                password: 'password123'
            });
            console.log('✅ Login endpoint working');
            console.log('🔑 Token received:', loginResponse.data.token ? 'Yes' : 'No');
            
            // Test 4: Test protected route with token
            if (loginResponse.data.token) {
                console.log('');
                console.log('4. Testing protected route...');
                const protectedResponse = await axios.get(`${API_BASE_URL}/api/users/me`, {
                    headers: {
                        'Authorization': `Bearer ${loginResponse.data.token}`
                    }
                });
                console.log('✅ Protected route working');
                console.log('👤 User data received:', protectedResponse.data.data ? 'Yes' : 'No');
            }
        } catch (error) {
            console.log('❌ Login failed:', error.response?.data?.message || error.message);
        }
        
        console.log('\n🎉 API Connection Test Complete!');
        
    } catch (error) {
        console.log('❌ Server connection failed:', error.message);
        console.log('💡 Make sure the server is running on port 5000');
    }
}

// Run the test
testAPIConnection();
