const Progress = require('../models/Progress');

// @desc    Create new progress entry
// @route   POST /api/progress
// @access  Private
const createProgress = async (req, res, next) => {
    try {
        // Add user to req.body
        req.body.userId = req.user.id;

        const progress = await Progress.create(req.body);

        res.status(201).json({
            success: true,
            data: progress
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Get all progress entries for a user
// @route   GET /api/progress
// @access  Private
const getProgress = async (req, res, next) => {
    try {
        const progress = await Progress.find({ userId: req.user.id }).sort('-createdAt');

        res.status(200).json({
            success: true,
            count: progress.length,
            data: progress
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Get single progress entry
// @route   GET /api/progress/:id
// @access  Private
const getProgressEntry = async (req, res, next) => {
    try {
        const progress = await Progress.findById(req.params.id);

        if (!progress) {
            return res.status(404).json({
                success: false,
                message: 'Progress entry not found'
            });
        }

        // Make sure user owns progress entry
        if (progress.userId.toString() !== req.user.id) {
            return res.status(401).json({
                success: false,
                message: 'Not authorized to access this progress entry'
            });
        }

        res.status(200).json({
            success: true,
            data: progress
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Update progress entry
// @route   PUT /api/progress/:id
// @access  Private
const updateProgress = async (req, res, next) => {
    try {
        let progress = await Progress.findById(req.params.id);

        if (!progress) {
            return res.status(404).json({
                success: false,
                message: 'Progress entry not found'
            });
        }

        // Make sure user owns progress entry
        if (progress.userId.toString() !== req.user.id) {
            return res.status(401).json({
                success: false,
                message: 'Not authorized to update this progress entry'
            });
        }

        progress = await Progress.findByIdAndUpdate(req.params.id, req.body, {
            new: true,
            runValidators: true
        });

        res.status(200).json({
            success: true,
            data: progress
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Delete progress entry
// @route   DELETE /api/progress/:id
// @access  Private
const deleteProgress = async (req, res, next) => {
    try {
        const progress = await Progress.findById(req.params.id);

        if (!progress) {
            return res.status(404).json({
                success: false,
                message: 'Progress entry not found'
            });
        }

        // Make sure user owns progress entry
        if (progress.userId.toString() !== req.user.id) {
            return res.status(401).json({
                success: false,
                message: 'Not authorized to delete this progress entry'
            });
        }

        await progress.deleteOne();

        res.status(200).json({
            success: true,
            data: {}
        });
    } catch (error) {
        next(error);
    }
};

// @desc    Get progress statistics for a user
// @route   GET /api/progress/stats
// @access  Private
const getProgressStats = async (req, res, next) => {
    try {
        const progress = await Progress.find({ userId: req.user.id }).sort('createdAt');

        if (progress.length === 0) {
            return res.status(200).json({
                success: true,
                data: {
                    totalEntries: 0,
                    weightChange: 0,
                    averageWeight: 0,
                    progress: []
                }
            });
        }

        const firstEntry = progress[0];
        const lastEntry = progress[progress.length - 1];
        const weightChange = lastEntry.weight - firstEntry.weight;
        const averageWeight = progress.reduce((sum, entry) => sum + entry.weight, 0) / progress.length;

        res.status(200).json({
            success: true,
            data: {
                totalEntries: progress.length,
                weightChange: weightChange.toFixed(2),
                averageWeight: averageWeight.toFixed(2),
                progress: progress
            }
        });
    } catch (error) {
        next(error);
    }
};

module.exports = {
    createProgress,
    getProgress,
    getProgressEntry,
    updateProgress,
    deleteProgress,
    getProgressStats
};

