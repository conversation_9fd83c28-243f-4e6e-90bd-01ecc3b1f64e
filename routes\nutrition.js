const express = require('express');
const {
    createNutritionPlan,
    getNutritionPlans,
    getNutritionPlan,
    updateNutritionPlan,
    deleteNutritionPlan
} = require('../controllers/nutrition');

const { protect } = require('../middleware/auth');

const router = express.Router();

// All routes are protected
router.use(protect);

router.route('/')
    .get(getNutritionPlans)
    .post(createNutritionPlan);

router.route('/:id')
    .get(getNutritionPlan)
    .put(updateNutritionPlan)
    .delete(deleteNutritionPlan);

module.exports = router;

