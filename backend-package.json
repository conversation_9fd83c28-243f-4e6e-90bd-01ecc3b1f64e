{"name": "fitverse-backend", "version": "1.0.0", "description": "FitVerse Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["fitness", "api", "nodejs", "express", "mongodb"], "author": "FitVerse Team", "license": "MIT"}