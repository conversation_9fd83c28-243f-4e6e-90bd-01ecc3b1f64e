# 🚀 FitVerse - Quick Start Guide

## ⚡ Quick Setup

### 1. Install Dependencies
```bash
npm install
```

### 2. Set Up Environment Variables
Create a `.env` file in the root directory:
```env
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb://localhost:27017/
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRE=30d
```

### 3. Start MongoDB
Make sure MongoDB is running on your system.

### 4. Start the Server
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

## 🧪 Test the API

### Using Postman
1. Import the `FitVerse_API.postman_collection.json` file into Postman
2. Set the `baseUrl` variable to `http://localhost:5000`
3. Start testing the endpoints

### Using curl
```bash
# Test the welcome endpoint
curl http://localhost:5000/

# Register a new user
curl -X POST http://localhost:5000/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password123",
    "goal": "loss",
    "weight": 80
  }'
```

## 📊 API Endpoints Summary

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/` | Welcome message | No |
| POST | `/api/users/register` | Register user | No |
| POST | `/api/users/login` | Login user | No |
| GET | `/api/users/me` | Get current user | Yes |
| GET | `/api/workouts` | Get user workouts | Yes |
| POST | `/api/workouts` | Create workout | Yes |
| GET | `/api/nutrition` | Get nutrition plans | Yes |
| POST | `/api/nutrition` | Create nutrition plan | Yes |
| GET | `/api/progress` | Get progress entries | Yes |
| POST | `/api/progress` | Create progress entry | Yes |
| GET | `/api/progress/stats` | Get progress stats | Yes |

## 🔐 Authentication

For protected routes, include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 🎯 Next Steps

1. **Test all endpoints** using Postman or curl
2. **Add more features** like workout templates
3. **Build a frontend** with React/Next.js
4. **Deploy to production** (Heroku, Vercel, etc.)

## 🆘 Troubleshooting

### MongoDB Connection Error
- Make sure MongoDB is running
- Check the connection string in `.env`
- Verify MongoDB is accessible on the specified port

### Port Already in Use
- Change the PORT in `.env` file
- Kill the process using the port: `npx kill-port 5000`

### JWT Token Issues
- Make sure JWT_SECRET is set in `.env`
- Check token expiration time
- Verify token format in Authorization header

## 📞 Support

If you encounter any issues, check the main README.md file for detailed documentation.
