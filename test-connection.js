const axios = require('axios');

async function testConnection() {
    console.log('🧪 اختبار الربط بين الفرونت إند والباك إند...\n');

    try {
        // اختبار الباك إند مباشرة
        console.log('1️⃣ اختبار الباك إند مباشرة...');
        const backendResponse = await axios.get('http://localhost:5000/');
        console.log('✅ الباك إند يعمل:', backendResponse.data.message);

        // اختبار API endpoints
        console.log('\n2️⃣ اختبار نقاط النهاية API...');
        const apiResponse = await axios.get('http://localhost:5000/api/users');
        console.log('✅ API يعمل بشكل صحيح');

        // اختبار البروكسي (من خلال الفرونت إند)
        console.log('\n3️⃣ اختبار البروكسي...');
        const proxyResponse = await axios.get('http://localhost:3000/api/users');
        console.log('✅ البروكسي يعمل بشكل صحيح');

        console.log('\n🎉 جميع الاختبارات نجحت! الربط يعمل بشكل مثالي.');
        console.log('\n📱 يمكنك الآن الوصول إلى:');
        console.log('   - الفرونت إند: http://localhost:3000');
        console.log('   - الباك إند API: http://localhost:5000');

    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);

        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 تأكد من أن الخوادم تعمل:');
            console.log('   - الباك إند: npm run server');
            console.log('   - الفرونت إند: cd fitverse-frontend && npm run dev');
        }
    }
}

testConnection();
