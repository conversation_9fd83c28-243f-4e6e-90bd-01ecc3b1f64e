const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 بدء تشغيل FitVerse...\n');

// تشغيل الباك إند
const server = spawn('node', ['server.js'], {
    stdio: 'inherit',
    shell: true
});

// انتظار قليل ثم تشغيل الفرونت إند
setTimeout(() => {
    const client = spawn('npm', ['run', 'dev'], {
        cwd: path.join(__dirname, 'fitverse-frontend'),
        stdio: 'inherit',
        shell: true
    });

    client.on('close', (code) => {
        console.log(`\n❌ الفرونت إند توقف مع الكود: ${code}`);
        server.kill();
    });
}, 2000);

server.on('close', (code) => {
    console.log(`\n❌ الباك إند توقف مع الكود: ${code}`);
});

// معالجة إيقاف المشروع
process.on('SIGINT', () => {
    console.log('\n🛑 إيقاف المشروع...');
    server.kill();
    process.exit();
});

process.on('SIGTERM', () => {
    console.log('\n🛑 إيقاف المشروع...');
    server.kill();
    process.exit();
});
